<?php

namespace App\Filament\Resources\UnitTypeResource\Pages;

use App\Filament\Resources\UnitTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateUnitType extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = UnitTypeResource::class;
    public function getTitle(): string
    {
        return __('resource.unit type resource.label');
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
